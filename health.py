import streamlit as st  
import pandas as pd 
import plotly.express as px 
import plotly.graph_objects as go
import base64
from streamlit_option_menu import option_menu
from datetime import timedelta
import warnings
import plotly.figure_factory as ff
warnings.filterwarnings('ignore')

st.set_page_config(page_title='Health Care Analysis',page_icon='🏥',layout='wide')

with open('style.css') as f:
    st.markdown(f'<style>{f.read()}</style>', unsafe_allow_html=True)


def get_img_as_base64(file):
    with open(file, "rb") as f:
        data = f.read()
    return base64.b64encode(data).decode()

img = get_img_as_base64("doctor.jpg")
page_background_image = f"""
<style>
[data-testid="stAppViewContainer"] > .main {{
    background-image: linear-gradient(rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.5)), url("data:image/png;base64,{img}");
    background-size: cover;
}}
</style>
"""

st.markdown(page_background_image, unsafe_allow_html=True)
df = pd.read_csv("healthcare_analysis.csv")
with st.sidebar:
    selected = option_menu("Select Visualization",  ["-",
    'Distribution of Blood Types',
    'Condition Counts by Gender',
    'Common Conditions',
    'Number of Appointments by Date',
    'Distribution of Patients Across Insurance Providers',
    'Age Distribution by Gender',
    'BMI Distribution by Condition',
    'Heart Rate vs. Temperature by Condition',
    'Condition by Gender and Blood Type',
    'Systolic and Diastolic Blood Pressure Distribution',
    'Number of Appointments per Month',
    'Correlation Heatmap of Vital Signs',
    'Gender Distribution in Each Condition',
    'Heart Rate by Gender and Condition',
    'Average Vital Signs by Conditon',
    'Density Heatmap of Age and BMI',
    'Temperature Distribution by Gender and Condition',
    'BMI Distribution by Gender and Blood Type',
    'Cumulative Distribution Function of BMI by Condition',
    '3D Scatter Plot of Age, Heart Rate, and BMI',
    'Box Plot of Blood Pressure Components by Condition',
], 
     menu_icon="cast",icons=['feather','feather','feather','feather','feather','feather','feather','feather','feather','feather',
                             'feather','feather','feather','feather','feather','feather','feather','feather','feather','feather','feather','feather','feather'] ,default_index=0)
    selected
    
if selected ==  'Distribution of Blood Types':
    blood_type_counts = df["Blood_Type"].value_counts()
    fig = px.bar(blood_type_counts, x=blood_type_counts.index, y=blood_type_counts.values, labels={'x':'Blood Type', 'y':'Count'}, title='Distribution of Blood Types')
    st.plotly_chart(fig)
    
if selected == 'Condition Counts by Gender':
    condition_gender_counts = df.groupby(['Condition', 'Gender']).size().reset_index(name='Count')
    fig = px.bar(condition_gender_counts, x='Condition', y='Count', color='Gender', barmode='group', title='Condition Counts by Gender')
    st.plotly_chart(fig)

    
if selected == 'Common Conditions':
    condition_counts = df["Condition"].value_counts()
    fig = px.bar(condition_counts, x=condition_counts.index, y=condition_counts.values, labels={'x':'Condition', 'y':'Count'}, title='Common Conditions')
    st.plotly_chart(fig)
    
# if selected ==  'Correlation between Vital Signs and BMI':
#     # Convert Blood Pressure to separate Systolic and Diastolic values
#     df[['Systolic_BP', 'Diastolic_BP']] = df['Blood_Pressure'].str.split('/', expand=True).astype(int)
#     # Scatter plot matrix
#     fig = px.scatter_matrix(df, dimensions=["Heart_Rate", "Temperature", "Systolic_BP", "Diastolic_BP", "BMI"], color="Condition", title="Correlation between Vital Signs and BMI")
#     st.plotly_chart(fig)
    
if selected == 'Number of Appointments by Date':
    # df['Appointment_Date'] = pd.to_datetime(df['Appointment_Date'], format='%d-%m-%Y')
    # appointments_by_date = df['Appointment_Date'].value_counts().sort_index()
    # st.write(appointments_by_date)
    # fig = px.line(appointments_by_date, x=appointments_by_date.index, y=appointments_by_date.values, labels={'x':'Date', 'y':'Number of Appointments'}, title='Number of Appointments by Date')
    # st.plotly_chart(fig)
    
    
    df['Appointment_Date'] = pd.to_datetime(df['Appointment_Date'], format='%d-%m-%Y')

    # Create a DataFrame for appointments by date
    appointments_by_date = df['Appointment_Date'].value_counts().sort_index()
    appointments_df = appointments_by_date.reset_index()
    appointments_df.columns = ['Date', 'Number of Appointments']

    # Convert to datetime.date for compatibility with Streamlit slider
    appointments_df['Date'] = appointments_df['Date'].dt.date

    # Define the date range for the slider
    min_date = appointments_df['Date'].min()
    max_date = appointments_df['Date'].max()

    # Set default date range to show the last 6 months from the current date
    default_end_date = max_date
    default_start_date = default_end_date - timedelta(days=6*30)  # Approximate 6 months

    # Slider for date range
    start_date, end_date = st.slider(
        'Select Date Range',
        min_value=min_date,
        max_value=max_date,
        value=(default_start_date, default_end_date),
        format='YYYY-MM-DD'
    )

    # Filter data based on selected date range
    filtered_df = appointments_df[(appointments_df['Date'] >= start_date) & (appointments_df['Date'] <= end_date)]

    # Plot
    fig = px.line(
        filtered_df,
        x='Date',
        y='Number of Appointments',
        labels={'Date': 'Date', 'Number of Appointments': 'Number of Appointments'},
        title='Number of Appointments by Date'
    )

    st.plotly_chart(fig)
    
if selected == 'Distribution of Patients Across Insurance Providers':
    # insurance_counts = df["Insurance_Provider"].value_counts()
    # fig = px.pie(insurance_counts, values=insurance_counts.values, names=insurance_counts.index, title='Distribution of Patients across Insurance Providers')
    # st.plotly_chart(fig)
    
    insurance_counts = df['Insurance_Provider'].value_counts()
    # Get the top 15 insurance providers
    top_15_providers = insurance_counts.head(20)
    top_15_providers_df = top_15_providers.reset_index()
    top_15_providers_df.columns = ['Insurance_Provider', 'Count']

    # Multiselect for top 15 insurance providers
    selected_providers = st.multiselect(
        'Select Insurance Providers',
        options=top_15_providers_df['Insurance_Provider'].tolist(),
        default=top_15_providers_df['Insurance_Provider'].head()
    )

    # Filter DataFrame based on selected providers
    filtered_df = df[df['Insurance_Provider'].isin(selected_providers)]

    # Recalculate insurance counts for the filtered data
    filtered_insurance_counts = filtered_df['Insurance_Provider'].value_counts()

    # Plot the pie chart
    fig = px.pie(
        names=filtered_insurance_counts.index,
        values=filtered_insurance_counts.values,
        title='Distribution of Patients across Insurance Providers'
    )

    st.plotly_chart(fig)
    
if selected == 'Age Distribution by Gender':
    fig = px.histogram(df, x='Age', color='Gender', marginal='box', nbins=10, title='Age Distribution by Gender')
    st.plotly_chart(fig)
    
if selected == 'BMI Distribution by Condition':
    fig = px.violin(df, y='BMI', x='Condition', color='Condition', box=True, points='all', title='BMI Distribution by Condition')
    st.plotly_chart(fig)
    
if selected == 'Heart Rate vs. Temperature by Condition':
    # fig = px.scatter(df, x='Temperature', y='Heart_Rate',color='Condition', symbol='Gender', title='Heart Rate vs. Temperature by Condition')
    # st.plotly_chart(fig)

    # Filters for Condition, Gender, and Blood Type
    condition_filter = st.multiselect('Select Condition(s)',options=df['Condition'].unique(),default=['Obesity'])
    gender_filter = st.multiselect('Select Gender(s)',options=df['Gender'].unique(),default=['Female'])
    blood_type_filter = st.multiselect('Select Blood Type(s)',options=df['Blood_Type'].unique(),default=['O+','B-','AB+'])

    # Filter DataFrame based on selected options
    filtered_df = df[(df['Condition'].isin(condition_filter)) &(df['Gender'].isin(gender_filter)) &(df['Blood_Type'].isin(blood_type_filter))]
    
     # Sample size input
    sample_size = st.number_input('Select Number of Data Points to Display', min_value=10, max_value=len(filtered_df), value=50, step=10)

    # Sample the filtered DataFrame
    sampled_df = filtered_df.sample(n=sample_size, random_state=1)

    # Plot
    fig = px.scatter(
        sampled_df,
        x='Temperature',
        y='Heart_Rate',
        color='Condition',
        symbol='Gender',
        size='BMI',
        hover_data=['Blood_Type'],
        title='Scatter Plot of Heart Rate vs. Temperature by Condition',
        labels={'Temperature': 'Temperature (°C)', 'Heart_Rate': 'Heart Rate (bpm)'}
    )
    
    fig.update_layout(
        xaxis_title='Temperature (°C)',
        yaxis_title='Heart Rate (bpm)',
        legend_title='Condition'
    )
    
    st.plotly_chart(fig)
    
if selected == 'Condition by Gender and Blood Type':
    st.subheader('Condition by Gender and Blood Type')

    # Filters for Gender and Blood Type
    gender_filter = st.multiselect(
        'Select Gender',
        options=df['Gender'].unique(),
        default=['Male']
    )
    blood_type_filter = st.multiselect(
        'Select Blood Type',
        options=df['Blood_Type'].unique(),
        default=['B+',"AB+","A+","O-"]
    )

    # Filter DataFrame based on selected options
    filtered_df = df[(df['Gender'].isin(gender_filter)) & (df['Blood_Type'].isin(blood_type_filter))]

    # Plot
    fig = px.sunburst(
        filtered_df,
        path=['Condition', 'Gender', 'Blood_Type'],
        title='Condition by Gender and Blood Type'
    )
    st.plotly_chart(fig)
    
if selected == 'Systolic and Diastolic Blood Pressure Distribution':
    fig = go.Figure()
    df[['Systolic_BP', 'Diastolic_BP']] = df['Blood_Pressure'].str.split('/', expand=True).astype(int)
    fig.add_trace(go.Histogram(x=df['Systolic_BP'], name='Systolic BP', opacity=0.75))
    fig.add_trace(go.Histogram(x=df['Diastolic_BP'], name='Diastolic BP', opacity=0.75))

    fig.update_layout(
        barmode='overlay',
        title='Systolic and Diastolic Blood Pressure Distribution',
        xaxis_title='Blood Pressure (mmHg)',
        yaxis_title='Count'
    )

    st.plotly_chart(fig)
    
if selected == 'Number of Appointments per Month':

    df['Appointment_Date'] = pd.to_datetime(df['Appointment_Date'])
    df['Appointment_Month'] = df['Appointment_Date'].dt.to_period('M')
    appointments_by_month = df['Appointment_Month'].value_counts().sort_index()

    fig = px.line(
        x=appointments_by_month.index.to_timestamp(),
        y=appointments_by_month.values,
        labels={'x': 'Month', 'y': 'Number of Appointments'},
        title='Number of Appointments per Month'
    )
    
    st.plotly_chart(fig)


if selected == 'Correlation Heatmap of Vital Signs':
    df[['Systolic_BP', 'Diastolic_BP']] = df['Blood_Pressure'].str.split('/', expand=True).astype(int)
    corr_matrix = df[['Heart_Rate', 'Temperature', 'Systolic_BP', 'Diastolic_BP', 'BMI']].corr()
    fig = px.imshow(corr_matrix, text_auto=True, aspect='auto', title='Correlation Heatmap of Vital Signs')
    st.plotly_chart(fig)
    
if selected == 'Gender Distribution in Each Condition':
    fig = px.pie(df, names='Condition', color='Gender', title='Gender Distribution in Each Condition')
    st.plotly_chart(fig)
    
if selected ==  'Heart Rate by Gender and Condition':
    fig = px.box(df, x='Gender', y='Heart_Rate', color='Condition', title='Heart Rate by Gender and Condition')
    st.plotly_chart(fig)

if selected == 'Average Vital Signs by Conditon':
    df[['Systolic_BP', 'Diastolic_BP']] = df['Blood_Pressure'].str.split('/', expand=True).astype(int)
    avg_vitals_by_condition = df.groupby('Condition').agg({
    'Heart_Rate': 'mean',
    'Temperature': 'mean',
    'Systolic_BP': 'mean',
    'Diastolic_BP': 'mean',
    'BMI': 'mean'
}).reset_index()

    print(avg_vitals_by_condition)
    fig = go.Figure()

    conditions = avg_vitals_by_condition['Condition'].unique()

    for condition in conditions:
        subset = avg_vitals_by_condition[avg_vitals_by_condition['Condition'] == condition]
        fig.add_trace(go.Scatterpolar(
            r=[subset['Heart_Rate'].values[0], subset['Temperature'].values[0], subset['Systolic_BP'].values[0], subset['Diastolic_BP'].values[0], subset['BMI'].values[0]],
            theta=['Heart_Rate', 'Temperature', 'Systolic_BP', 'Diastolic_BP', 'BMI'],
            fill='toself',
            name=condition
        ))

    fig.update_layout(title='Average Vital Signs by Condition', polar=dict(radialaxis=dict(visible=True)))
    st.plotly_chart(fig)
    
if selected ==  'Density Heatmap of Age and BMI':
    fig = px.density_heatmap(df, x='Age', y='BMI', marginal_x='histogram', marginal_y='histogram', title='Density Heatmap of Age and BMI')
    st.plotly_chart(fig)

if selected == 'Temperature Distribution by Gender and Condition':
    # fig = px.violin(df, y='Temperature', x='Condition', color='Gender', box=True, points='all', title='Temperature Distribution by Gender and Condition')
    # st.plotly_chart(fig)
    st.subheader('Temperature Distribution by Gender and Condition')
    
    # Filters
    gender_filter = st.multiselect('Select Gender', options=df['Gender'].unique(), default=df['Gender'].unique())
    condition_filter = st.multiselect('Select Condition', options=df['Condition'].unique(), default=['Diabetes','Asthma','Hypertension'])

    # Filtered DataFrame
    filtered_df = df[(df['Gender'].isin(gender_filter)) & (df['Condition'].isin(condition_filter))]

    # Plot
    fig = px.violin(filtered_df, y='Temperature', x='Condition', color='Gender', box=True, points='all', title='Temperature Distribution by Gender and Condition')
    st.plotly_chart(fig)
    
if selected == 'BMI Distribution by Gender and Blood Type':
    # fig = px.box(df, x='Blood_Type', y='BMI', color='Gender', title='BMI Distribution by Gender and Blood Type')
    # st.plotly_chart(fig)
    st.subheader('BMI Distribution by Gender and Blood Type')
    
    # Filters
    gender_filter = st.multiselect('Select Gender', options=df['Gender'].unique(), default=df['Gender'].unique())
    blood_type_filter = st.multiselect('Select Blood Type', options=df['Blood_Type'].unique(), default=['B+','AB+','A+'])

    # Filtered DataFrame
    filtered_df = df[(df['Gender'].isin(gender_filter)) & (df['Blood_Type'].isin(blood_type_filter))]

    # Plot
    fig = px.box(filtered_df, x='Blood_Type', y='BMI', color='Gender', title='BMI Distribution by Gender and Blood Type')
    st.plotly_chart(fig)
if selected == 'Cumulative Distribution Function of BMI by Condition':
    # fig = px.ecdf(df, x='BMI', color='Condition', title='Cumulative Distribution Function of BMI by Condition')
    # st.plotly_chart(fig)
    # Filters
    gender_filter = st.multiselect('Select Gender', options=df['Gender'].unique(), default=df['Gender'].unique())
    condition_filter = st.multiselect('Select Condition', options=df['Condition'].unique(), default=['Hypertension','Obesity','Asthma'])
    # Filtered DataFrame
    filtered_df = df[(df['Gender'].isin(gender_filter)) & (df['Condition'].isin(condition_filter))]
    # CDF Plot
    fig = ff.create_distplot([filtered_df[filtered_df['Condition'] == condition]['BMI'] for condition in condition_filter],
                             group_labels=condition_filter, show_hist=False, show_rug=False)
    st.plotly_chart(fig)
    
if selected == '3D Scatter Plot of Age, Heart Rate, and BMI':
    # fig = px.scatter_3d(df, x='Age', y='Heart_Rate', z='BMI', color='Condition', symbol='Gender', title='3D Scatter Plot of Age, Heart Rate, and BMI')
    # st.plotly_chart(fig)
    st.subheader('3D Scatter Plot of Age, Heart Rate, and BMI')
    # Filters
    gender_filter = st.multiselect('Select Gender', options=df['Gender'].unique(), default=['Male'])
    condition_filter = st.multiselect('Select Condition', options=df['Condition'].unique(), default=['Obesity', 'Hypertension'])
     # Filtered DataFrame
    filtered_df = df[(df['Gender'].isin(gender_filter)) & (df['Condition'].isin(condition_filter))]
    # Sample size input
    sample_size = st.number_input('Select Number of Data Points to Display', min_value=10, max_value=len(filtered_df), value=70, step=10)
    # Sample the filtered DataFrame
    sampled_df = filtered_df.sample(n=sample_size, random_state=1)
    # 3D Scatter Plot
    fig = px.scatter_3d(sampled_df, x='Age', y='Heart_Rate', z='BMI', color='Condition', symbol='Gender', title='3D Scatter Plot of Age, Heart Rate, and BMI')
    st.plotly_chart(fig)
    
if selected == 'Box Plot of Blood Pressure Components by Condition':
    df[['Systolic_BP', 'Diastolic_BP']] = df['Blood_Pressure'].str.split('/', expand=True).astype(int)
    df_melted = df.melt(id_vars=['Condition'], value_vars=['Systolic_BP', 'Diastolic_BP'], var_name='Blood Pressure Type', value_name='Value')
    fig = px.box(df_melted, x='Condition', y='Value', color='Blood Pressure Type', title='Box Plot of Blood Pressure Components by Condition')
    st.plotly_chart(fig)
    
